from rest_framework import serializers
from .models import Auction, <PERSON><PERSON>ate<PERSON><PERSON>, Bid, AutoBid, AuctionImage, AuctionWatchlist, SellerLocation
from accounts.serializers import UserSerializer


class FishCategorySerializer(serializers.ModelSerializer):
    """Serializer for fish categories"""
    
    class Meta:
        model = FishCategory
        fields = ['id', 'name', 'description', 'image', 'is_active']


class AuctionImageSerializer(serializers.ModelSerializer):
    """Serializer for auction images"""
    
    class Meta:
        model = AuctionImage
        fields = ['id', 'image', 'caption', 'order']


class BidSerializer(serializers.ModelSerializer):
    """Serializer for bids"""

    bidder = UserSerializer(read_only=True)
    auction_id = serializers.IntegerField(source='auction.id', read_only=True)

    class Meta:
        model = Bid
        fields = ['id', 'amount', 'bid_type', 'timestamp', 'is_winning', 'bidder', 'auction_id']
        read_only_fields = ['id', 'timestamp', 'is_winning', 'bidder', 'auction_id']


class AutoBidSerializer(serializers.ModelSerializer):
    """Serializer for automatic bidding"""
    
    class Meta:
        model = AutoBid
        fields = ['id', 'max_amount', 'increment', 'is_active', 'current_bid_amount']
        read_only_fields = ['id', 'current_bid_amount']


class AuctionListSerializer(serializers.ModelSerializer):
    """Serializer for auction list view"""

    seller = UserSerializer(read_only=True)
    fish_category = FishCategorySerializer(read_only=True)
    latest_bid = serializers.SerializerMethodField()
    time_remaining = serializers.SerializerMethodField()
    is_watched = serializers.SerializerMethodField()
    main_image = serializers.SerializerMethodField()

    class Meta:
        model = Auction
        fields = [
            'id', 'title', 'fish_type', 'weight', 'quantity', 'starting_price',
            'current_price', 'buy_now_price', 'auction_type', 'status', 'start_time', 'end_time',
            'main_image', 'total_bids', 'total_bidders', 'views_count',
            'seller', 'fish_category', 'latest_bid', 'time_remaining', 'is_watched'
        ]
    
    def get_latest_bid(self, obj):
        latest_bid = obj.bids.first()
        if latest_bid:
            return BidSerializer(latest_bid).data
        return None
    
    def get_time_remaining(self, obj):
        return str(obj.time_remaining)
    
    def get_is_watched(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return AuctionWatchlist.objects.filter(user=request.user, auction=obj).exists()
        return False

    def get_main_image(self, obj):
        if obj.main_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.main_image.url)
            return obj.main_image.url
        return None


class AuctionDetailSerializer(serializers.ModelSerializer):
    """Serializer for auction detail view"""

    seller = UserSerializer(read_only=True)
    winner = UserSerializer(read_only=True)
    fish_category = FishCategorySerializer(read_only=True)
    images = AuctionImageSerializer(many=True, read_only=True)
    bids = BidSerializer(many=True, read_only=True)
    latest_bid = serializers.SerializerMethodField()
    time_remaining = serializers.SerializerMethodField()
    is_watched = serializers.SerializerMethodField()
    user_auto_bid = serializers.SerializerMethodField()
    main_image = serializers.SerializerMethodField()
    has_live_location = serializers.BooleanField(read_only=True)

    class Meta:
        model = Auction
        fields = [
            'id', 'title', 'description', 'fish_type', 'weight', 'quantity',
            'catch_date', 'catch_location', 'auction_type', 'starting_price',
            'reserve_price', 'target_price', 'buy_now_price', 'current_price', 'bid_increment',
            'start_time', 'end_time', 'status', 'winner', 'payment_deadline', 'payment_received',
            'main_image', 'total_bids', 'total_bidders', 'views_count',
            'created_at', 'updated_at', 'seller', 'fish_category', 'images',
            'bids', 'latest_bid', 'time_remaining', 'is_watched', 'user_auto_bid',
            'latitude', 'longitude', 'location_updated_at', 'is_location_live', 'has_live_location',
            'delivery_address'
        ]
    
    def get_latest_bid(self, obj):
        latest_bid = obj.bids.first()
        if latest_bid:
            return BidSerializer(latest_bid).data
        return None
    
    def get_time_remaining(self, obj):
        return str(obj.time_remaining)
    
    def get_is_watched(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return AuctionWatchlist.objects.filter(user=request.user, auction=obj).exists()
        return False
    
    def get_user_auto_bid(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            try:
                auto_bid = AutoBid.objects.get(auction=obj, bidder=request.user)
                return AutoBidSerializer(auto_bid).data
            except AutoBid.DoesNotExist:
                pass
        return None

    def get_main_image(self, obj):
        if obj.main_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.main_image.url)
            return obj.main_image.url
        return None


class AuctionCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating auctions"""
    
    images = AuctionImageSerializer(many=True, required=False)
    
    class Meta:
        model = Auction
        fields = [
            'title', 'description', 'fish_category', 'fish_type', 'weight',
            'quantity', 'catch_date', 'catch_location', 'auction_type',
            'starting_price', 'reserve_price', 'target_price', 'buy_now_price', 'bid_increment',
            'start_time', 'end_time', 'main_image', 'images',
            'latitude', 'longitude', 'delivery_address'
        ]
    
    def create(self, validated_data):
        images_data = validated_data.pop('images', [])
        auction = Auction.objects.create(**validated_data)
        
        for image_data in images_data:
            AuctionImage.objects.create(auction=auction, **image_data)
        
        return auction


class PlaceBidSerializer(serializers.Serializer):
    """Serializer for placing bids"""
    
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    
    def validate_amount(self, value):
        auction = self.context['auction']
        
        if value <= auction.current_price:
            raise serializers.ValidationError("Bid amount must be higher than current price")
        
        if auction.status != 'live':
            raise serializers.ValidationError("Auction is not live")
        
        return value


class AuctionWatchlistSerializer(serializers.ModelSerializer):
    """Serializer for auction watchlist"""

    auction = AuctionListSerializer(read_only=True)

    class Meta:
        model = AuctionWatchlist
        fields = ['id', 'auction', 'added_at']
        read_only_fields = ['id', 'added_at']


class SellerLocationSerializer(serializers.ModelSerializer):
    """Serializer for seller location tracking"""

    seller_name = serializers.CharField(source='seller.username', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)

    class Meta:
        model = SellerLocation
        fields = [
            'id', 'seller', 'seller_name', 'auction', 'latitude', 'longitude',
            'accuracy', 'altitude', 'heading', 'speed', 'status', 'status_display',
            'address', 'is_active', 'timestamp'
        ]
        read_only_fields = ['id', 'seller', 'seller_name', 'status_display', 'timestamp']


class UpdateSellerLocationSerializer(serializers.Serializer):
    """Serializer for updating seller location"""

    latitude = serializers.DecimalField(max_digits=10, decimal_places=8)
    longitude = serializers.DecimalField(max_digits=11, decimal_places=8)
    accuracy = serializers.FloatField(required=False, allow_null=True)
    altitude = serializers.FloatField(required=False, allow_null=True)
    heading = serializers.FloatField(required=False, allow_null=True)
    speed = serializers.FloatField(required=False, allow_null=True)
    status = serializers.ChoiceField(choices=SellerLocation.STATUS_CHOICES, required=False)
    address = serializers.CharField(max_length=500, required=False, allow_blank=True)
    auction_id = serializers.IntegerField(required=False, allow_null=True)


class AuctionLocationSerializer(serializers.ModelSerializer):
    """Serializer for auction with location data"""

    latest_seller_location = SellerLocationSerializer(read_only=True)
    has_live_location = serializers.BooleanField(read_only=True)

    class Meta:
        model = Auction
        fields = [
            'id', 'title', 'latitude', 'longitude', 'location_updated_at',
            'is_location_live', 'latest_seller_location', 'has_live_location'
        ]
