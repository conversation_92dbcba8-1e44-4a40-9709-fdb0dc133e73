from django.urls import path
from . import views

urlpatterns = [
    # Broker services
    path('services/', views.BrokerServiceListView.as_view(), name='broker-services-list'),
    
    # Service requests
    path('requests/create/', views.ServiceRequestCreateView.as_view(), name='service-request-create'),
    path('requests/my/', views.ServiceRequestListView.as_view(), name='my-service-requests'),
    path('requests/available/', views.AvailableServiceRequestsView.as_view(), name='available-service-requests'),
    path('requests/<uuid:service_request_id>/quotes/', views.ServiceRequestQuotesView.as_view(), name='service-request-quotes'),
    
    # Broker quotes
    path('quotes/create/', views.BrokerQuoteCreateView.as_view(), name='broker-quote-create'),
    path('requests/<uuid:service_request_id>/select-broker/<uuid:quote_id>/', views.SelectBrokerView.as_view(), name='select-broker'),
    
    # Service executions
    path('executions/broker/', views.BrokerServiceExecutionsView.as_view(), name='broker-service-executions'),
    path('executions/client/', views.ClientServiceExecutionsView.as_view(), name='client-service-executions'),
    path('executions/<uuid:pk>/update/', views.UpdateServiceExecutionView.as_view(), name='update-service-execution'),
    path('executions/<uuid:execution_id>/release-payment/', views.release_payment, name='release-payment'),
    
    # Client feedback
    path('executions/<uuid:pk>/feedback/', views.SubmitClientFeedbackView.as_view(), name='submit-client-feedback'),
    
    # Broker profile
    path('profile/', views.BrokerProfileView.as_view(), name='broker-profile'),
]
